* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: #2d3436;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #6c5ce7;
    letter-spacing: -0.02em;
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #636e72;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #6c5ce7;
}

.contact-btn {
    background: #6c5ce7;
    color: white !important;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    background: #5f3dc4;
}

/* 主页面 */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #6c5ce7 0%, #00b894 50%, #fd79a8 100%);
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h1 {
    font-size: 4rem;
    font-weight: 700;
    color: white;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.highlight {
    background: linear-gradient(45deg, #00b894, #fd79a8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text p {
    font-size: 1.25rem;
    color: rgba(255,255,255,0.8);
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn-primary, .btn-secondary {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: white;
    color: #6c5ce7;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(108, 92, 231, 0.3);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.1);
}

/* 浮动卡片 */
.hero-visual {
    position: relative;
    height: 500px;
}

.floating-card {
    position: absolute;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.floating-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-1 {
    width: 200px;
    height: 250px;
    top: 50px;
    left: 50px;
    animation: float 6s ease-in-out infinite;
}

.card-2 {
    width: 180px;
    height: 220px;
    top: 150px;
    right: 80px;
    animation: float 6s ease-in-out infinite 2s;
}

.card-3 {
    width: 160px;
    height: 200px;
    bottom: 80px;
    left: 150px;
    animation: float 6s ease-in-out infinite 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(2deg); }
}

/* 作品展示 */
.works {
    padding: 8rem 0;
    background: #f8f9fa;
}

/* 瀑布流样式 */
.works-grid {
    column-count: 3;
    column-gap: 16px;
    width: 100%;
    max-width: 900px;
    margin: 20px auto;
    padding: 0 16px;
    box-sizing: border-box;
}

.work-item {
    break-inside: avoid;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    cursor: pointer;
}

.work-item .flip-container {
    position: relative;
    width: 100%;
    perspective: 1000px;
    cursor: pointer;
}

.flip-container .flip-front,
.flip-container .flip-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    transition: transform 0.6s;
    border-radius: 8px;
    overflow: hidden;
}

.flip-container .flip-front {
    transform: rotateY(0deg);
}

.flip-container .flip-back {
    transform: rotateY(180deg);
}

.work-item.flipped .flip-front {
    transform: rotateY(180deg);
}

.work-item.flipped .flip-back {
    transform: rotateY(0deg);
}

.flip-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #f8f8f8;
    display: block;
}

/* 响应式 */
@media (max-width: 768px) {
    .works-grid {
        column-count: 2;
        column-gap: 8px;
        padding: 0 8px;
    }
    
    .work-item {
        margin-bottom: 8px;
    }
}

/* 服务项目 */
.services {
    padding: 8rem 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.service-card.featured {
    background: linear-gradient(135deg, #6c5ce7, #fd79a8);
    color: white;
    transform: scale(1.05);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.service-card p {
    color: #636e72;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.service-card.featured p {
    color: rgba(255,255,255,0.8);
}

.service-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #6c5ce7;
}

.service-card.featured .service-price {
    color: #fff;
}

.popular-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: #00b894;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
}

/* 联系我们 */
.contact {
    padding: 8rem 0;
    background: #2d3436;
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.contact-info p {
    font-size: 1.25rem;
    color: #94a3b8;
    margin-bottom: 3rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-method .icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: #6c5ce7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-method h4 {
    margin-bottom: 0.25rem;
}

.contact-method p {
    color: #94a3b8;
    margin: 0;
}

.contact-form {
    background: #636e72;
    padding: 3rem;
    border-radius: 20px;
}

.form-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-form input,
.contact-form textarea {
    background: #74b9ff;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    color: white;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
    color: rgba(255,255,255,0.7);
}

.submit-btn {
    width: 100%;
    background: #6c5ce7;
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.submit-btn:hover {
    background: #5f3dc4;
}

/* 图文介绍区域 */
.about-process {
    padding: 8rem 0;
    background: #fafafa;
}

.process-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 6rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 2rem;
}

.process-item.reverse {
    direction: rtl;
}

.process-item.reverse > * {
    direction: ltr;
}

.process-content {
    position: relative;
}

.process-number {
    font-size: 4rem;
    font-weight: 700;
    color: #e5e7eb;
    position: absolute;
    top: -2rem;
    left: -1rem;
    z-index: 1;
}

.process-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.process-content p {
    font-size: 1.1rem;
    color: #6b7280;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.process-content ul {
    list-style: none;
    padding: 0;
}

.process-content li {
    padding: 0.5rem 0;
    color: #374151;
    position: relative;
    padding-left: 2rem;
}

.process-content li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.process-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    padding: 10px;
    background: white;
}

.process-image.single {
    grid-template-columns: 1fr;
    padding: 0;
}

.process-image img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    transition: transform 0.3s ease;
    border-radius: 10px;
}

.process-image.single img {
    height: 400px;
    border-radius: 20px;
}

.process-image img:hover {
    transform: scale(1.05);
}

/* 响应式 */
@media (max-width: 768px) {
    .process-item {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-bottom: 4rem;
    }
    
    .process-item.reverse {
        direction: ltr;
    }
    
    .process-number {
        font-size: 3rem;
        top: -1rem;
        left: 0;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .works-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }
    
    .work-item.large,
    .work-item.wide {
        grid-column: span 1;
        grid-row: span 1;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .form-group {
        grid-template-columns: 1fr;
    }
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 3.5rem;
    font-weight: 700;
    color: #2d3436;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.section-header p {
    font-size: 1.4rem;
    color: #636e72;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 响应式标题 */
@media (max-width: 768px) {
    .section-header h2 {
        font-size: 2.5rem;
    }
    
    .section-header p {
        font-size: 1.2rem;
    }
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.close-btn {
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.3s;
}

.close-btn:hover {
    color: #333;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.id-box {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.copy-btn {
    padding: 0.4rem 0.8rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.upload-area p {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #333;
}

.upload-area small {
    color: #666;
    font-size: 0.9rem;
}

.preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
}

.image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.delete-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 68, 68, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#requirements {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-family: inherit;
    resize: vertical;
}

#contact {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

.form-buttons {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn-cancel, .btn-submit {
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
}

.btn-submit {
    background: #007bff;
    color: white;
}

.btn-submit:hover {
    background: #0056b3;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .modal-content {
        margin: 5% auto;
        padding: 1.5rem;
        width: 95%;
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    .btn-cancel, .btn-submit {
        width: 100%;
    }
}
