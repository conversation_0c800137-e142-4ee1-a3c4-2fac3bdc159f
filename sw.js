const CACHE_NAME = 'comic-art-cache-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json',
  '/images1/cat_head.png',
  '/images1/image (21).png',
  '/images1/d7f1.webp',
  '/images1/d7f2.png',
  '/images1/d7f3.png',
  '/images1/d7f4.webp',
  '/images1/d7f5.jpg',
  '/images1/d7f8.png',
  '/images1/d7f10.png',
  '/images1/d7f11.png',
  '/images1/d7f12.png',
  '/images1/d7f13.png',
  '/images1/d7f14.png',
  '/images1/d7f18.png',
  '/images1/d7f19.png',
  '/images1/d7f20.png',
  '/images1/d7f21.png',
  '/images1/d7f25.png',
  '/images1/d7f26.jpeg',
  '/images1/d7f27.jpeg',
  '/images1/d7f28.png',
  '/images2/compareImages.json',
  '/images2/d7f8d.png',
  '/images2/d7f10d.png',
  '/images2/d7f14z.png',
  '/images2/d7f14d.png'
];

// 安装Service Worker
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// 拦截网络请求
self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // 如果缓存中有响应，则返回缓存的响应
        if (response) {
          return response;
        }
        
        // 否则从网络获取
        return fetch(event.request).then(
          response => {
            // 检查是否收到有效响应
            if(!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // 克隆响应
            const responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });

            return response;
          }
        );
      })
  );
});

// 更新Service Worker
self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];
  
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
}); 