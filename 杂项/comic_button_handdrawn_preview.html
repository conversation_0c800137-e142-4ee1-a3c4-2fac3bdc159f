<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>图片翻转卡牌效果</title>
  <style>
    body {
      background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-family: 'Comic Sans MS', cursive, sans-serif;
    }
    .flip-card-container {
      width: 350px;
      height: 470px;
      perspective: 1200px;
      margin: 40px auto;
    }
    .flip-card {
      width: 100%;
      height: 100%;
      position: relative;
      transition: transform 0.2s linear;
      transform-style: preserve-3d;
      cursor: pointer;
    }
    .flip-card.flipped {
      transform: rotateY(180deg);
    }
    .flip-card-face {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 18px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0,0,0,0.18), 0 2px 8px #222;
      border: 5px solid #222;
      background: #fff;
      backface-visibility: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .flip-card-front img, .flip-card-back img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
    .flip-card-back {
      transform: rotateY(180deg);
    }
  </style>
</head>
<body>
  <div class="flip-card-container">
    <div class="flip-card" id="flipCard">
      <div class="flip-card-face flip-card-front">
        <img src="zhengmian.png" alt="正面图片">
      </div>
      <div class="flip-card-face flip-card-back">
        <img src="beimian.jpg" alt="背面图片">
      </div>
    </div>
  </div>
  <script>
    const flipCard = document.getElementById('flipCard');
    let isFlipped = false;
    flipCard.addEventListener('click', () => {
      isFlipped = !isFlipped;
      flipCard.classList.toggle('flipped', isFlipped);
    });
  </script>
</body>
</html> 