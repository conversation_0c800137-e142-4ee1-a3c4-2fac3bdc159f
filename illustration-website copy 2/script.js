// 作品翻转功能
document.addEventListener('DOMContentLoaded', function() {
    // 为所有作品项添加点击翻转事件
    document.querySelectorAll('.work-item').forEach(item => {
        item.addEventListener('click', function() {
            this.classList.toggle('flipped');
        });
    });
});

// FAQ折叠功能
function toggleFAQ(num) {
    const answer = document.getElementById('answer' + num);
    const icon = document.getElementById('icon' + num);
    
    // 先关闭所有其他FAQ
    for (let i = 1; i <= 5; i++) {
        if (i !== num) {
            const otherAnswer = document.getElementById('answer' + i);
            const otherIcon = document.getElementById('icon' + i);
            if (otherAnswer && otherIcon) {
                otherAnswer.style.display = 'none';
                otherIcon.textContent = '+';
            }
        }
    }
    
    // 切换当前FAQ
    if (answer.style.display === 'none' || answer.style.display === '') {
        answer.style.display = 'block';
        icon.textContent = '−';
    } else {
        answer.style.display = 'none';
        icon.textContent = '+';
    }
}

// 全局变量
let uploadedPhotos = [];
let uploadedStyles = [];
const MAX_PHOTOS = 5;
const MAX_STYLES = 3;
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// 打开服务弹窗
function openServiceModal(serviceType, serviceName, price) {
    document.getElementById('serviceTitle').textContent = serviceName + ' - 定制服务';
    document.getElementById('orderId').textContent = generateOrderId();
    
    // 显示弹窗
    const modal = document.getElementById('serviceModal');
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 重置表单
    resetForm();
}

// 关闭弹窗
function closeServiceModal() {
    const modal = document.getElementById('serviceModal');
    modal.style.display = 'none';
    document.body.style.overflow = '';
}

// 生成订单ID
function generateOrderId() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return 'ORD' + timestamp.slice(-6) + random;
}

// 复制订单ID
function copyOrderId() {
    const orderId = document.getElementById('orderId').textContent;
    if (navigator.clipboard) {
        navigator.clipboard.writeText(orderId).then(() => {
            alert('订单ID已复制！');
        });
    } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea');
        textArea.value = orderId;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('订单ID已复制！');
    }
}

// 处理照片上传
function handlePhotoUpload(event) {
    const files = Array.from(event.target.files);
    
    if (uploadedPhotos.length + files.length > MAX_PHOTOS) {
        alert(`最多只能上传${MAX_PHOTOS}张照片`);
        return;
    }
    
    files.forEach(file => {
        if (!validateFile(file)) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            uploadedPhotos.push({
                name: file.name,
                data: e.target.result
            });
            displayPhotoPreview();
        };
        reader.readAsDataURL(file);
    });
    
    event.target.value = '';
}

// 处理风格图上传
function handleStyleUpload(event) {
    const files = Array.from(event.target.files);
    
    if (uploadedStyles.length + files.length > MAX_STYLES) {
        alert(`最多只能上传${MAX_STYLES}张风格图`);
        return;
    }
    
    files.forEach(file => {
        if (!validateFile(file)) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            uploadedStyles.push({
                name: file.name,
                data: e.target.result
            });
            displayStylePreview();
        };
        reader.readAsDataURL(file);
    });
    
    event.target.value = '';
}

// 验证文件
function validateFile(file) {
    if (file.size > MAX_FILE_SIZE) {
        alert(`文件 ${file.name} 超过5MB限制`);
        return false;
    }
    
    if (!file.type.startsWith('image/')) {
        alert(`文件 ${file.name} 不是图片格式`);
        return false;
    }
    
    return true;
}

// 显示照片预览
function displayPhotoPreview() {
    const preview = document.getElementById('photoPreview');
    preview.innerHTML = '';
    
    uploadedPhotos.forEach((photo, index) => {
        const container = document.createElement('div');
        container.className = 'image-item';
        container.innerHTML = `
            <img src="${photo.data}" alt="${photo.name}">
            <button type="button" class="delete-btn" onclick="removePhoto(${index})">×</button>
        `;
        preview.appendChild(container);
    });
}

// 显示风格图预览
function displayStylePreview() {
    const preview = document.getElementById('stylePreview');
    preview.innerHTML = '';
    
    uploadedStyles.forEach((style, index) => {
        const container = document.createElement('div');
        container.className = 'image-item';
        container.innerHTML = `
            <img src="${style.data}" alt="${style.name}">
            <button type="button" class="delete-btn" onclick="removeStyle(${index})">×</button>
        `;
        preview.appendChild(container);
    });
}

// 删除照片
function removePhoto(index) {
    uploadedPhotos.splice(index, 1);
    displayPhotoPreview();
}

// 删除风格图
function removeStyle(index) {
    uploadedStyles.splice(index, 1);
    displayStylePreview();
}

// 提交订单
function submitOrder() {
    const requirements = document.getElementById('requirements').value.trim();
    const contact = document.getElementById('contact').value.trim();
    
    // 验证必填项
    if (uploadedPhotos.length === 0) {
        alert('请至少上传一张照片！');
        return;
    }
    
    if (!requirements) {
        alert('请填写详细需求描述！');
        return;
    }
    
    if (!contact) {
        alert('请填写联系方式！');
        return;
    }
    
    // 模拟提交成功
    alert('订单提交成功！我们会在24小时内联系您确认详细需求。请保存好您的订单ID。');
    closeServiceModal();
}

// 重置表单
function resetForm() {
    document.getElementById('requirements').value = '';
    document.getElementById('contact').value = '';
    uploadedPhotos = [];
    uploadedStyles = [];
    displayPhotoPreview();
    displayStylePreview();
}

// 点击弹窗外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('serviceModal');
    if (event.target === modal) {
        closeServiceModal();
    }
}
