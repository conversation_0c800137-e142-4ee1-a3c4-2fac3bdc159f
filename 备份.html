<!--
图片与对比逻辑说明：
1. 封面图全部放在 images1 文件夹，命名如 d1f2、d1f3 等，d=大容器，f=分镜编号，编号仅作标识。
2. 对比图全部放在 images2 文件夹。
   - 真人图命名为 d1f2z1、d1f2z2 ... （z=真人，数字为对比组编号）
   - 动漫图命名为 d1f2d1、d1f2d2 ... （d=动漫，数字为对比组编号）
3. 点击 images1 中的封面图（如 d1f2）时，系统会自动查找 images2 中所有以 d1f2z* 和 d1f2d* 命名的图片，按编号配对为对比组。
4. 图片顺序可随意打乱，点击后对比内容只与图片名绑定，与容器顺序无关。
5. 只要命名规范，系统即可自动配对所有对比组。
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="description" content="专业漫画风格艺术展示，提供真人转动漫风格定制服务" />
  <meta name="keywords" content="漫画风格,艺术展示,真人转动漫,定制服务,AI绘画" />
  <meta name="author" content="漫画艺术工作室" />
  <meta name="robots" content="index, follow" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://your-domain.com/" />
  <meta property="og:title" content="漫画风格艺术展示" />
  <meta property="og:description" content="专业漫画风格艺术展示，提供真人转动漫风格定制服务" />
  <meta property="og:image" content="杂项/fengmiantu.jpg" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://your-domain.com/" />
  <meta property="twitter:title" content="漫画风格艺术展示" />
  <meta property="twitter:description" content="专业漫画风格艺术展示，提供真人转动漫风格定制服务" />
  <meta property="twitter:image" content="杂项/fengmiantu.jpg" />

  <!-- PWA -->
  <meta name="theme-color" content="#f8f4e3" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="漫画艺术" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="images1/tubiao.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="images1/tubiao.png" />
  <link rel="apple-touch-icon" href="images1/tubiao.png" />
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="manifest.json" />
  
  <title>漫画风格艺术展示 - 专业真人转动漫定制服务</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    html, body {
      min-height: 100vh;
      background-color: #f5b000;
      background-image: url("https://www.transparenttextures.com/patterns/brick-wall-dark.png");
      font-family: 'ZCOOL KuaiLe', 'Comic Sans MS', cursive, sans-serif;
      /* 隐藏滚动条但保持滚动功能 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
    }

    /* 隐藏 Webkit 浏览器的滚动条 */
    html::-webkit-scrollbar,
    body::-webkit-scrollbar {
      display: none;
    }

    /* 顶部社交栏样式 */
    .social-section {
      background: rgba(255,255,255,0.95);
      border-radius: 0;
      width: 100%;
      max-width: 800px;
      margin: 60px auto 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      position: relative;
    }

    .cover-image {
      width: 100%;
      max-width: 800px;
      border-radius: 0;
      box-shadow: 0 4px 24px rgba(0,0,0,0.18);
      margin-bottom: 0;
      margin-top: 0;
      object-fit: cover;
    }

    .logo-image {
      width: 120px;
      display: block;
      margin: 0 auto 1.5rem auto;
    }

    /* 大容器样式 */
    .comic-container {
      position: relative;
      width: 100%;
      max-width: 800px;
      aspect-ratio: 3/4;
      margin: 0 auto;
      background: #f8f4e3;
      border-radius: 0;
      border: 12px solid #fff;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0,0,0,0.15),
                  0 0 0 1px rgba(0,0,0,0.05);
      padding: 1.5rem;
      box-sizing: border-box;
    }

    /* 分镜面板样式 */
    .panel {
      position: absolute;
      border: 1px solid #111;
      border-radius: 0.375rem;
      overflow: hidden;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
    }

    .panel:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0,0,0,0.2);
      z-index: 10;
    }

    .panel::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
      z-index: 2;
    }

    .panel:hover::before {
      transform: translateX(100%);
    }

    .panel img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      user-select: none;
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      pointer-events: none;
    }

    .panel-number {
      position: absolute;
      top: 0.625rem;
      left: 0.625rem;
      background: rgba(0,0,0,0.5);
      color: #fff;
      padding: 0.125rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 1.2em;
      z-index: 10;
    }

    /* 分镜位置类 */
    .panel-1 {
      left: 1rem;
      top: 1rem;
      width: calc(50% - 1rem);
      height: calc(50% - 1rem);
      z-index: 5;
    }

    .panel-2 {
      left: 50%;
      top: 1rem;
      width: calc(50% - 1rem);
      height: calc(50% - 1rem);
      z-index: 4;
    }

    .panel-3 {
      left: 1rem;
      top: 50%;
      width: calc(50% - 1rem);
      height: calc(25% - 1rem);
      z-index: 3;
    }

    .panel-4 {
      left: 50%;
      top: 50%;
      width: calc(50% - 1rem);
      height: calc(25% - 1rem);
      z-index: 2;
    }

    .panel-5 {
      left: 1rem;
      top: 75%;
      width: calc(50% - 1rem);
      height: calc(25% - 1rem);
      z-index: 1;
    }

    .panel-6 {
      left: 50%;
      top: 75%;
      width: calc(50% - 1rem);
      height: calc(25% - 1rem);
      z-index: 1;
      border-width: 1px 0 1px 1px; /* 上、左、下有线，右边无线 */
      border-style: solid;
      border-color: #111;
      border-radius: 0.375rem;
    }

    /* 按钮样式 */
    .btn {
      padding: 0.5rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 600;
      color: #fff;
      transition: all 0.2s;
      cursor: pointer;
      border: 2px solid #000;
      box-shadow: 4px 4px 0px 0px rgba(0,0,0,1);
    }

    .btn:hover {
      box-shadow: 2px 2px 0px 0px rgba(0,0,0,1);
      transform: translate(2px, 2px);
    }

    .btn-green {
      background: linear-gradient(to right, #10b981, #059669);
    }

    .btn-green:hover {
      background: linear-gradient(to right, #059669, #047857);
    }

    .btn-blue {
      background: linear-gradient(to right, #3b82f6, #2563eb);
    }

    .btn-blue:hover {
      background: linear-gradient(to right, #2563eb, #1d4ed8);
    }

    /* 模态框样式 */
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.85);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      z-index: 1000;
      padding: 20px;
      box-sizing: border-box;
    }

    .modal.show {
      display: flex !important;
      align-items: center;
      justify-content: center;
    }

    .modal-content {
      border-radius: 20px !important;
      border: 2px solid #222 !important;
      box-shadow: 0 4px 24px rgba(0,0,0,0.10) !important;
      background: #fff !important;
      width: 90%;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      position: relative;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }

    .compare-modal-content {
      max-width: 900px;
      width: 96vw;
      position: relative;
      min-height: 400px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: visible;
      position: relative;
    }

    .compare-tip {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(59, 130, 246, 0.9);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      z-index: 20;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .compare-tip span {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    /* 备用居中方法 */
    .modal-content.centered {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      margin: 0;
    }

    .form-content {
      flex: 1;
      overflow-y: auto;
      padding-right: 10px;
    }

    .form-buttons {
      position: sticky;
      bottom: 0;
      background: white;
      padding-top: 20px;
      border-top: 1px solid #eee;
      margin-top: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .id-box {
      background: #f5f5f5;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: monospace;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .copy-btn {
      background: #4a90e2;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .copy-btn:hover {
      background: #357abd;
    }

    .upload-area {
      border: 2px dashed #ccc;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      margin-bottom: 10px;
      transition: border-color 0.2s;
    }

    .upload-area:hover {
      border-color: #4a90e2;
    }

    .preview-image {
      max-width: 100px;
      max-height: 100px;
      margin: 5px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .preview-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }

    /* 对比弹窗样式 */
    .compare-flex {
      display: flex;
      flex-direction: row;
      gap: 1rem;
      justify-content: center;
      align-items: center;
      padding: 2rem 0;
      width: 100%;
    }

    .compare-image {
      width: 100%;
      max-width: 360px;
      max-height: 60vh;
      height: auto;
      object-fit: contain;
      border-radius: 0;
      box-shadow: 0 2px 12px rgba(0,0,0,0.10);
      user-select: none;
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      pointer-events: none;
      display: block;
      margin: 0 auto;
    }

    .nav-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      font-size: 2em;
      background: none;
      border: none;
      cursor: pointer;
      z-index: 10;
      padding: 0.5rem;
      border-radius: 50%;
      transition: background-color 0.2s;
    }

    .nav-btn:hover {
      background: rgba(0,0,0,0.1);
    }

    .nav-btn.prev {
      left: 0.5rem;
    }

    .nav-btn.next {
      right: 0.5rem;
    }

    /* 关闭按钮样式 */
    .close-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      z-index: 20;
      color: #666;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
    }

    .close-btn:hover {
      background: rgba(0,0,0,0.1);
      color: #333;
    }

    /* 空数据提示样式 */
    .no-data-message {
      text-align: center;
      padding: 40px;
      color: #666;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 300px;
      width: 100%;
    }

    .no-data-message p {
      margin: 0;
    }

    .no-data-message p:first-child {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .no-data-message p:last-child {
      font-size: 14px;
      opacity: 0.8;
    }

    /* 加载状态 */
    .loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .spinner {
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3498db;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      display: inline-block;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 响应式设计 */
    @media (max-width: 700px) {
      .social-section {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
      .top-header-row {
        gap: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        align-items: flex-end !important;
      }
      .top-header-row img[alt="侧面人物"] {
        width: 40vw !important;
        max-width: 160px !important;
        min-width: 80px !important;
        margin: 0 !important;
        padding: 0 !important;
        display: block;
        object-fit: contain;
        align-self: flex-end;
      }
    }

    /* 错误状态 */
    .error {
      border-color: #ef4444 !important;
      color: #ef4444;
    }

    .success {
      border-color: #10b981 !important;
      color: #10b981;
    }

    /* 新增：页面加载动画 */
    .page-loader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #f8f4e3;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease-out;
    }

    .page-loader.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .loader-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e5e7eb;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* 新增：图片预加载指示器 */
    .image-preloader {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 30px;
      height: 30px;
      border: 2px solid #e5e7eb;
      border-top: 2px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 5;
    }

    /* 新增：滚动到顶部按钮 */
    .scroll-to-top {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 50px;
      height: 50px;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: none;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transition: all 0.3s ease;
      z-index: 100;
    }

    .scroll-to-top:hover {
      background: #2563eb;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0,0,0,0.2);
    }

    .scroll-to-top.show {
      display: flex;
    }

    /* 新增：响应式改进 */
    @media (max-width: 480px) {
      .social-section {
        margin-top: 40px !important;
      }
      .cover-image {
        margin-bottom: 0;
      }
      .logo-image {
        width: 80px;
        margin: 0 auto 1rem auto;
      }
      .comic-container {
        margin: 0 auto;
        padding: 0.5rem;
      }
      .panel-number {
        font-size: 1em;
        padding: 1px 6px;
      }
      .btn {
        padding: 0.4rem 1.2rem;
        font-size: 0.9rem;
      }
    }

    /* 新增：无障碍支持 */
    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    /* 新增：焦点样式 */
    .btn:focus,
    .panel:focus,
    .close-btn:focus {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }

    /* 新增：禁用所有图片下载和选择 */
    img {
      user-select: none;
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      pointer-events: none;
    }

    /* 允许图片容器点击 */
    .panel, .upload-area, .preview-container {
      pointer-events: auto;
    }

    /* 允许图片预览区域交互 */
    .preview-image {
      pointer-events: none;
    }

    .logo-social-center {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 12px;
      margin-bottom: 12px;
    }

    .top-header-row {
      display: flex;
      align-items: center;
      width: 100%;
      gap: 24px;
      margin-top: 12px;
      margin-bottom: 12px;
      justify-content: space-between;
    }

    .logo-social-col {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .comic-container-2 .panel2 {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    /* 分镜布局样式 */
    .bottom-container .comic-panel {
      position: relative;
      width: 100%;
      height: 1000px;
    }

    .bottom-container .panel {
      position: absolute;
      border: 1px solid #111;
      border-radius: 0.375rem;
      overflow: hidden;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
    }

    /* 形状样式 */
    .bottom-container .panel.circle {
      border-radius: 50%;
    }

    .bottom-container .panel.triangle {
      clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    }

    .bottom-container .panel.hexagon {
      clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
    }

    .bottom-container .panel:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0,0,0,0.2);
      z-index: 10;
    }

    .bottom-container .panel img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      user-select: none;
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      pointer-events: none;
    }

    .bottom-container .panel-number {
      position: absolute;
      top: 0.625rem;
      left: 0.625rem;
      background: rgba(0,0,0,0.5);
      color: #fff;
      padding: 0.125rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 1.2em;
      z-index: 10;
    }

    /* 分镜位置和大小 */
    .bottom-container .panel-0 {
      left: 9px;
      top: 4px;
      width: 283px;
      height: 283px;
    }

    .bottom-container .panel-1 {
      left: 602px;
      top: 58px;
      width: 318px;
      height: 281px;
      transform: rotate(30deg);
    }

    .bottom-container .panel-2 {
      left: 564px;
      top: 343px;
      width: 0px;
      height: 0px;
    }

    .bottom-container .panel-3 {
      left: 306px;
      top: 189px;
      width: 311px;
      height: 135px;
    }

    /* 新增分镜容器样式 */
    .comic-container-7 {
      column-count: 3;
      column-gap: 8px;
      width: 100%;
      max-width: 800px;
      margin: 20px auto;
      background: #000;
      padding: 8px;
      box-sizing: border-box;
      min-height: 200px;
      display: block;
    }
    @media (max-width: 700px) {
      .comic-container-7 {
        column-count: 2;
      }
    }
    .comic-container-7 .auto-img-item {
      break-inside: avoid;
      margin-bottom: 8px;
      background: #222;
      border-radius: 6px;
      overflow: hidden;
      display: block;
      position: relative;
      cursor: pointer;
      transition: transform 0.3s, box-shadow 0.3s;
      width: 100%;
    }
    .comic-container-7 .auto-img-item img {
      width: 100%;
      height: auto;
      display: block;
    }
    .comic-container-7 .auto-img-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
      z-index: 2;
      pointer-events: none;
    }
    .comic-container-7 .auto-img-item:hover::before {
      transform: translateX(100%);
    }
    .comic-container-7 .panel-number {
      position: absolute;
      top: 0.625rem;
      left: 0.625rem;
      background: rgba(0,0,0,0.5);
      color: #fff;
      padding: 0.125rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 1.2em;
      z-index: 10;
      pointer-events: none;
    }
    .comic-container-7 .auto-img-item:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0,0,0,0.2);
      z-index: 10;
    }
    #makeSameBtn {
      position: absolute;
      left: 50%;
      bottom: 24px;
      transform: translateX(-50%);
      margin: 0;
      font-size: 1.2em;
      z-index: 30;
    }

    /* 瀑布流样式 */
    .compare-waterfall {
      column-count: 2;
      column-gap: 16px;
      width: 100%;
      max-width: 900px;
      margin: 20px auto;
      padding: 0 16px;
      box-sizing: border-box;
    }

    .compare-item {
      break-inside: avoid;
      margin-bottom: 16px;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      cursor: pointer;
    }

    .compare-item .flip-container {
      position: relative;
      width: 100%;
      /* 移除固定的 aspect-ratio: 3/4; */
      perspective: 1000px;
      cursor: pointer;
    }

    .compare-item .flip-container::after {
      content: '🔄';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0,0,0,0.8);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      opacity: 1;
      transition: opacity 0.3s;
      z-index: 10;
      animation: ripple 2s infinite;
    }

    @keyframes ripple {
      0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.9), 0 0 0 0 rgba(255, 255, 255, 0.9);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0), 0 0 0 20px rgba(255, 255, 255, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0), 0 0 0 0 rgba(255, 255, 255, 0);
      }
    }

    .compare-item:hover .flip-container::after {
      opacity: 1;
    }

    .compare-item .flip-front,
    .compare-item .flip-back {
      position: absolute;
      width: 100%;
      height: 100%;
      backface-visibility: hidden;
      transition: transform 0.6s;
    }

    .compare-item .flip-back {
      transform: rotateY(180deg);
    }

    .compare-item.flipped .flip-front {
      transform: rotateY(180deg);
    }

    .compare-item.flipped .flip-back {
      transform: rotateY(0);
    }

    .compare-item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .compare-item .label {
      padding: 8px;
      text-align: center;
      background: rgba(0,0,0,0.05);
      font-size: 14px;
    }

    @media (max-width: 700px) {
      .compare-waterfall {
        column-gap: 8px;
        padding: 0 8px;
      }
      
      .compare-item {
        margin-bottom: 8px;
      }
      
      .compare-item .label {
        padding: 4px;
        font-size: 12px;
      }
    }

    /* 原来的规则，现在只对flip-icon生效
    .compare-item .flip-container::after {
      content: '🔄';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0,0,0,0.8);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      opacity: 1;
      transition: opacity 0.3s;
      z-index: 10;
      animation: ripple 2s infinite;
    }
    */

    .compare-item .flip-container.flip-icon::after {
      content: '🔄';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0,0,0,0.9);
      color: white;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      opacity: 1;
      transition: opacity 0.3s;
      z-index: 10;
      animation: ripple 2s infinite, blink 1.5s infinite;
      border: 3px solid white;
      box-shadow: 0 0 20px rgba(0,0,0,0.8), 0 0 40px rgba(255,255,255,0.6);
    }

    @keyframes blink {
      0%, 50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
      25%, 75% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1.1);
      }
    }

    /* 水波纹效果 */
    .ripple-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      overflow: hidden;
    }

    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(59, 130, 246, 0.3);
      transform: scale(0);
      animation: ripple-animation 2s linear infinite;
      pointer-events: none;
    }

    .ripple:nth-child(1) {
      width: 100px;
      height: 100px;
      left: 20%;
      top: 30%;
      animation-delay: 0s;
    }

    .ripple:nth-child(2) {
      width: 150px;
      height: 150px;
      left: 60%;
      top: 50%;
      animation-delay: 0.5s;
    }

    .ripple:nth-child(3) {
      width: 80px;
      height: 80px;
      left: 40%;
      top: 70%;
      animation-delay: 1s;
    }

    .ripple:nth-child(4) {
      width: 120px;
      height: 120px;
      left: 80%;
      top: 20%;
      animation-delay: 1.5s;
    }

    @keyframes ripple-animation {
      0% {
        transform: scale(0);
        opacity: 1;
      }
      100% {
        transform: scale(4);
        opacity: 0;
      }
    }
  </style>
</head>
<body class="font-sans">
  <!-- 页面加载器 -->
  <div id="pageLoader" class="page-loader">
    <div class="loader-spinner"></div>
  </div>

  <!-- 顶部社交栏 -->
  <section class="social-section" style="position: relative;">
    <img src="images1/cat_head.png" alt="可爱猫头" style="position: absolute; top: -64px; right: 0; width: 120px; height: auto; z-index: 10;">
    <!-- 漫画封面图（已替换） -->
    <img src="杂项/fengmiantu.jpg" alt="封面图" class="cover-image">
  </section>

  <!-- 文字区域 -->
  <div id="textSection" style="width: 100%; max-width: 800px; margin: 0 auto; position: relative;">
    <img src="杂项/wenzitupian.jpg" alt="文字图" style="width: 100%; height: auto; display: block;">
    
    <!-- 百分比自适应的点击区域，宽39%，高45%，距离左边7%，距离顶部32%，完全透明无文字 -->
    <div
      onclick="openFAQModal()"
      style="
        position: absolute;
        left: 7%;
        top: 32%;
        width: 39%;
        height: 45%;
        background: transparent;
        cursor: pointer;
        z-index: 20;
        border-radius: 12px;
        /* 无需居中内容，无需文字 */"
      title="点击查看常见问题"
    ></div>
    
    <!-- FAQ水波纹效果 -->
    <div class="ripple-container" style="width:60px;height:36px;position:absolute;left:17%;top:64%;pointer-events:none;overflow:visible;">
      <div class="ripple" style="width:30px;height:30px;left:50%;top:50%;background:rgba(255,255,255,0.8);border-radius:50%;animation:ripple 2s infinite;transform:translate(-50%,-50%);box-shadow:0 0 0 0 rgba(255,255,255,1),0 0 0 0 rgba(255,255,255,1);"></div>
    </div>
    <div class="flex justify-center gap-8" style="z-index:10;position:absolute;top:32px;left:0;right:0;width:100%;">
      <a href="https://wa.me/你的号码" target="_blank" class="btn btn-green" style="min-width:120px;text-align:center;">💬 Contact</a>
      <button onclick="openPurchaseModal()" class="btn btn-blue" style="min-width:120px;text-align:center;">🛒 Pro Custom</button>
    </div>
  </div>

  <!-- 作品展示区 -->
  <main>
    <!-- 新增分镜容器 -->
    <div class="comic-container-7" id="autoCompareContainer"></div>

    
    
    <!-- 底部按钮 -->
    <!-- 已移动到文字区域，删除此处按钮 -->
    
    <footer class="text-center py-8 text-gray-600" style="position:relative;z-index:10;">
      <p>© 2024 漫画艺术工作室. All rights reserved.</p>
    </footer>
  </main>

  <!-- 滚动到顶部按钮 -->
  <button id="scrollToTop" class="scroll-to-top" onclick="scrollToTop()" aria-label="滚动到顶部">
    ↑
  </button>

  <!-- 购买表单模态框（定制订单） -->
  <div id="purchaseModal" class="modal">
    <div class="modal-content">
      <h2 class="text-2xl font-bold mb-6 text-center">定制订单</h2>
      <div style="color:#ef4444;font-size:0.95em;text-align:center;margin-bottom:16px;">重要说明：我们会参考封面图风格为您创作，但由于每个人的特征不同，最终效果与展示案例不会完全一模一样，仅供风格参考，请知悉。</div>
      <form id="purchaseForm">
        <div class="form-content">
          <!-- ID部分 -->
          <div class="form-group">
            <label class="form-label">你的专属ID（可通过此ID查询订单状态）</label>
            <div class="id-box">
              <span id="orderId"></span>
              <button type="button" class="copy-btn" onclick="copyOrderId()">复制</button>
            </div>
          </div>
          <!-- 照片上传 -->
          <div class="form-group">
            <label class="form-label">上传你本人的照片（三张最佳，必须包含正面照）</label>
            <div class="upload-area" onclick="document.getElementById('photoUpload').click()">
              <p>点击或拖拽上传照片</p>
              <input type="file" id="photoUpload" multiple accept="image/*" style="display: none" onchange="handlePhotoUpload(event)">
            </div>
            <div id="photoPreview" class="preview-container"></div>
            <div id="photoError" class="text-red-500 text-sm mt-1" style="display: none;"></div>
          </div>
          <!-- 风格图上传（定制专用） -->
          <div class="form-group" id="styleUploadArea">
            <label class="form-label">上传你喜欢的风格图（注意：不涉及图片内容）</label>
            <div class="upload-area" onclick="document.getElementById('styleUpload').click()">
              <p>点击或拖拽上传风格图</p>
              <input type="file" id="styleUpload" multiple accept="image/*" style="display: none" onchange="handleStyleUpload(event)">
            </div>
            <div id="stylePreview" class="preview-container"></div>
            <div id="styleError" class="text-red-500 text-sm mt-1" style="display: none;"></div>
          </div>
          <!-- 内容输入（定制专用） -->
          <div class="form-group" id="contentInputArea">
            <label class="form-label">输入你想要的内容，你想要扮演的角色</label>
            <textarea id="contentInput" class="w-full p-2 border border-gray-300 rounded" rows="4" placeholder="请详细描述你的需求..."></textarea>
            <div class="text-sm text-gray-500 mt-1">
              请简明扼要地描述您的需求
            </div>
          </div>
          <!-- 隐藏字段：分镜编号 -->
          <input type="hidden" id="productId" name="productId" value="">
        </div>
        <!-- 按钮 -->
        <div class="form-buttons">
          <div class="flex justify-end gap-4">
            <button type="button" class="btn bg-gray-500 hover:bg-gray-600" onclick="closeModal()">取消</button>
            <button type="button" id="submitBtn" class="btn btn-blue" onclick="goToPayment()">
              <span class="spinner" style="display: none;"></span>
              下一步
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- 制作同款表单模态框 -->
  <div id="makeSameModal" class="modal">
    <div class="modal-content">
      <h2 class="text-2xl font-bold mb-6 text-center">制作同款</h2>
      <div style="color:#ef4444;font-size:0.95em;text-align:center;margin-bottom:16px;">重要说明：我们会参考封面图风格为您创作，但由于每个人的特征不同，最终效果与展示案例不会完全一模一样，仅供风格参考，请知悉。</div>
      <form id="makeSameForm">
        <div class="form-content">
          <!-- ID部分 -->
          <div class="form-group">
            <label class="form-label">你的专属ID（可通过此ID查询订单状态）</label>
            <div class="id-box">
              <span id="makeSameOrderId"></span>
              <button type="button" class="copy-btn" onclick="copyMakeSameOrderId()">复制</button>
            </div>
          </div>
          <!-- 照片上传 -->
          <div class="form-group">
            <label class="form-label">上传你本人的照片（三张最佳，必须包含正面照）</label>
            <div class="upload-area" onclick="document.getElementById('makeSamePhotoUpload').click()">
              <p>点击或拖拽上传照片</p>
              <input type="file" id="makeSamePhotoUpload" multiple accept="image/*" style="display: none" onchange="handleMakeSamePhotoUpload(event)">
            </div>
            <div id="makeSamePhotoPreview" class="preview-container"></div>
            <div id="makeSamePhotoError" class="text-red-500 text-sm mt-1" style="display: none;"></div>
          </div>
          <!-- 隐藏字段：分镜编号 -->
          <input type="hidden" id="makeSameProductId" name="makeSameProductId" value="">
        </div>
        <!-- 按钮 -->
        <div class="form-buttons">
          <div class="flex justify-end gap-4">
            <button type="button" class="btn bg-gray-500 hover:bg-gray-600" onclick="closeMakeSameModal()">取消</button>
            <button type="button" id="makeSameSubmitBtn" class="btn btn-blue" onclick="goToMakeSamePayment()">
              <span class="spinner" style="display: none;"></span>
              下一步
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- 对比弹窗（Midjourney风格体验版） -->
  <div id="compareModal" class="modal" onclick="closeCompareModal(event)">
    <div class="modal-content compare-modal-content" onclick="event.stopPropagation()">
      <!-- 关闭按钮 -->
      <button class="close-btn" onclick="closeCompareModal({target:{id:'compareModal'}})">&times;</button>
      
      <!-- 瀑布流容器 -->
      <div class="compare-waterfall">
        <!-- 对比图会动态插入这里 -->
      </div>
      
      <!-- 空数据提示 -->
      <div id="noDataMessage" class="no-data-message" style="display:none;">
        <p>暂无对比数据</p>
        <p>该分镜的对比图片正在准备中...</p>
      </div>
      
      <!-- 制作同款按钮 -->
      <button id="makeSameBtn" class="btn btn-green" onclick="openMakeSameModal()">Same Custom</button>
    </div>
  </div>

  <!-- FAQ弹窗 -->
  <div id="faqModal" class="modal">
    <div class="modal-content" style="max-width:500px; position:relative;">
      <button class="close-btn" onclick="closeFAQModal()" style="position:fixed; top:20px; right:20px; z-index:1001; background:none; color:#ff4444; border:none; font-size:24px; cursor:pointer;">&times;</button>
      <h2 class="text-2xl font-bold mb-4 text-center">Service Guide</h2>
      <div style="line-height:1.8;">
        <!-- 问题1 -->
        <div class="faq-item mb-3">
          <div class="faq-question" onclick="toggleFAQ(1)" style="cursor:pointer; padding:12px; background:#f8f9fa; border-radius:8px; font-weight:600; display:flex; justify-content:space-between; align-items:center;">
            <span>1. What do I need to prepare?</span>
            <span class="faq-icon" id="icon1">+</span>
          </div>
          <div class="faq-answer" id="answer1" style="display:none; padding:15px; border-left:3px solid #3b82f6; margin-left:10px; margin-top:10px;">
            <p>To achieve the best results, please prepare the following materials:</p>
            <ul style="margin-left: 20px; margin-bottom: 15px;">
              <li><strong>Clear Photos:</strong> Provide 2-3 high-quality photos with clear visibility and facial details. Avoid excessive blur or shadows (at least one fully unobstructed front-facing photo is required).</li>
              <li><strong>Preferred Style Image:</strong> Submit 1 image of your preferred style, which can be any picture. Preferably choose from anime, manga, cartoon, or illustration types (avoid real photos, extremely detailed realistic illustrations, or abstract or surreal images).</li>
              <li><strong>Desired Content:</strong> Provide a brief description (within 1-2 sentences) of the idea or vision in your mind. Simple image content works best (the content of your style image and photos will not be replicated or involved in the final design).</li>
            </ul>
          </div>
        </div>

        <!-- 问题2 -->
        <div class="faq-item mb-3">
          <div class="faq-question" onclick="toggleFAQ(2)" style="cursor:pointer; padding:12px; background:#f8f9fa; border-radius:8px; font-weight:600; display:flex; justify-content:space-between; align-items:center;">
            <span>2. What's a Style Reference Image?</span>
            <span class="faq-icon" id="icon2">+</span>
          </div>
          <div class="faq-answer" id="answer2" style="display:none; padding:15px; border-left:3px solid #3b82f6; margin-left:10px; margin-top:10px;">
            <p>A Style Reference Image is an anime or illustration work you'd like us to reference, used for:</p>
            <ul style="margin-left: 20px; margin-bottom: 15px;">
              <li>Determining the overall artistic style.</li>
              <li>Referencing color schemes and lighting effects.</li>
              <li>Understanding your preferred drawing techniques.</li>
            </ul>
            <p>It can be any anime character, illustration, or artistic style you like. However, the specific content depicted in the image will not be replicated or reproduced.</p>
          </div>
        </div>

        <!-- 问题3 -->
        <div class="faq-item mb-3">
          <div class="faq-question" onclick="toggleFAQ(3)" style="cursor:pointer; padding:12px; background:#f8f9fa; border-radius:8px; font-weight:600; display:flex; justify-content:space-between; align-items:center;">
            <span>3. Is it free to try?</span>
            <span class="faq-icon" id="icon3">+</span>
          </div>
          <div class="faq-answer" id="answer3" style="display:none; padding:15px; border-left:3px solid #3b82f6; margin-left:10px; margin-top:10px;">
            <p>Currently, we provide the following service modes:</p>
            <ul style="margin-left: 20px; margin-bottom: 15px;">
              <li><strong>Pro Custom:</strong> High-quality, tailored service based on your ideas.</li>
              <li><strong>Same Custom:</strong> Simple creation based on our existing styles.</li>
              <li><strong>Sample Display:</strong> The website features numerous comparison images showcasing our production quality.</li>
              <li><strong>Quality Guarantee:</strong> If you're unsatisfied with the final product, we offer adjustments until you're satisfied (3 free adjustments for pro custom, 1 free adjustment for same custom).</li>
            </ul>
          </div>
        </div>

        <!-- 问题4 -->
        <div class="faq-item mb-3">
          <div class="faq-question" onclick="toggleFAQ(4)" style="cursor:pointer; padding:12px; background:#f8f9fa; border-radius:8px; font-weight:600; display:flex; justify-content:space-between; align-items:center;">
            <span>4. How do I make a purchase?</span>
            <span class="faq-icon" id="icon4">+</span>
          </div>
          <div class="faq-answer" id="answer4" style="display:none; padding:15px; border-left:3px solid #3b82f6; margin-left:10px; margin-top:10px;">
            <p>The purchasing process is very simple:</p>
            <ul style="margin-left: 20px; margin-bottom: 15px;">
              <li>Click the "Pro Custom" button to enter the custom page.</li>
              <li>Upload your photos and style reference image.</li>
              <li>Fill in your specific requirements and contact information.</li>
              <li>Select a payment method to complete the transaction.</li>
              <li>Wait for us to start production.</li>
            </ul>
            <p>Clicking on an existing cover image on the website will allow for same-style creation based on that cover's style. We've also uploaded examples of customer works based on cover images for your reference.</p>
          </div>
        </div>

        <!-- 问题5 -->
        <div class="faq-item mb-3">
          <div class="faq-question" onclick="toggleFAQ(5)" style="cursor:pointer; padding:12px; background:#f8f9fa; border-radius:8px; font-weight:600; display:flex; justify-content:space-between; align-items:center;">
            <span>5. How do I get the finished work?</span>
            <span class="faq-icon" id="icon5">+</span>
          </div>
          <div class="faq-answer" id="answer5" style="display:none; padding:15px; border-left:3px solid #3b82f6; margin-left:10px; margin-top:10px;">
            <p>We offer a digital delivery service:</p>
            <ul style="margin-left: 20px; margin-bottom: 15px;">
              <li><strong>Delivery Time:</strong> Typically completed within 1-3 working days, depending on complexity.</li>
              <li><strong>Delivery Method:</strong> High-quality finished files will be sent via your provided email or other contact method.</li>
              <li><strong>File Format:</strong> Provided in PNG and JPEG formats.</li>
              <li><strong>After-Sales Service:</strong> For modifications or adjustments, please contact us.</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 配置常量
    const CONFIG = {
      MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
      ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
      MAX_PHOTOS: 5,
      MAX_STYLES: 3
    };

    // 初始化所有功能
    document.addEventListener('DOMContentLoaded', function() {
      initializeApp();
      monitorPerformance();
      initLazyLoading();
      restoreFormData();
      
      // 注册Service Worker
      registerServiceWorker();
    });

    // 注册Service Worker
    function registerServiceWorker() {
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('Service Worker注册成功:', registration.scope);
            })
            .catch(function(error) {
              console.log('Service Worker注册失败:', error);
            });
        });
      }
    }

    // 初始化应用
    function initializeApp() {
      // 隐藏页面加载器
      setTimeout(() => {
        const loader = document.getElementById('pageLoader');
        loader.classList.add('hidden');
        setTimeout(() => {
          loader.style.display = 'none';
        }, 500);
      }, 1000);

      // 初始化滚动监听
      initScrollListener();
      
      // 初始化图片预加载
      preloadImages();
      
      // 初始化本地存储
      initLocalStorage();

      // 生成初始订单ID
      document.getElementById('orderId').textContent = generateOrderId();
    }

    // 滚动监听
    function initScrollListener() {
      const scrollBtn = document.getElementById('scrollToTop');
      const textSection = document.getElementById('textSection');
      window.addEventListener('scroll', function() {
        if (textSection) {
          const rect = textSection.getBoundingClientRect();
          if (rect.bottom <= 0) {
            scrollBtn.classList.add('show');
          } else {
            scrollBtn.classList.remove('show');
          }
        }
      });
    }

    // 滚动到顶部栏底部
    function scrollToTop() {
      window.scrollTo({
        top: 500, // 绝对500像素
        behavior: 'smooth'
      });
    }

    // 预加载图片
    function preloadImages() {
      const imagesToPreload = [
        'images1/cat_head.png',
        'images1/image (21).png',
        'images1/d7f1.webp',
        'images1/d7f2.png',
        'images1/d7f3.png',
        'images1/d7f4.webp',
        'images1/d7f5.jpg',
        'images1/d7f8.png',
        'images1/d7f10.png',
        'images1/d7f11.png',
        'images1/d7f12.png',
        'images1/d7f13.png',
        'images1/d7f14.png',
        'images1/d7f18.png',
        'images1/d7f19.png',
        'images1/d7f20.png',
        'images1/d7f21.png',
        'images1/d7f25.png',
        'images1/d7f26.jpeg',
        'images1/d7f27.jpeg',
        'images1/d7f28.png'
      ];

      imagesToPreload.forEach(url => {
        const img = new Image();
        img.onload = function() {
          console.log(`图片预加载成功: ${url}`);
        };
        img.onerror = function() {
          console.warn(`图片预加载失败: ${url}`);
        };
        img.src = url;
      });
    }

    // 本地存储初始化
    function initLocalStorage() {
      // 保存用户偏好设置
      if (!localStorage.getItem('userPreferences')) {
        localStorage.setItem('userPreferences', JSON.stringify({
          theme: 'light',
          language: 'zh-CN'
        }));
      }
    }


    // 生成随机ID
    function generateOrderId() {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let id = '';
      for (let i = 0; i < 8; i++) {
        id += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return id;
    }

    // 复制ID
    async function copyOrderId() {
      const id = document.getElementById('orderId').textContent;
      try {
        // 尝试使用现代API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(id);
          showMessage('ID已复制到剪贴板！', 'success');
        } else {
          // 降级方案：使用传统方法
          const textArea = document.createElement('textarea');
          textArea.value = id;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          
          try {
            document.execCommand('copy');
            showMessage('ID已复制到剪贴板！', 'success');
          } catch (err) {
            showMessage('复制失败，请手动复制', 'error');
          }
          
          textArea.remove();
        }
      } catch (err) {
        showMessage('复制失败，请手动复制', 'error');
      }
    }

    // 显示消息
    function showMessage(message, type = 'info') {
      const modal = document.getElementById('purchaseModal');
      const alertDiv = document.createElement('div');
      alertDiv.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-[1001] ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
      }`;
      alertDiv.textContent = message;
      modal.appendChild(alertDiv);
      setTimeout(() => alertDiv.remove(), 3000);
    }

    // 验证文件
    function validateFile(file) {
      if (!CONFIG.ALLOWED_TYPES.includes(file.type)) {
        return '只支持 JPG、PNG、WebP 格式的图片';
      }
      if (file.size > CONFIG.MAX_FILE_SIZE) {
        return '文件大小不能超过 5MB';
      }
      return null;
    }

    // 处理照片上传
    function handlePhotoUpload(event) {
      event.preventDefault();
      const files = Array.from(event.target.files);
      const preview = document.getElementById('photoPreview');
      const errorDiv = document.getElementById('photoError');
      
      // 清空错误提示
      errorDiv.style.display = 'none';
      errorDiv.textContent = '';
      
      // 检查总数是否超过限制
      const currentCount = preview.children.length;
      if (currentCount + files.length > CONFIG.MAX_PHOTOS) {
        errorDiv.textContent = `最多只能上传 ${CONFIG.MAX_PHOTOS} 张照片，当前已有 ${currentCount} 张`;
        errorDiv.style.display = 'block';
        return;
      }

      files.forEach((file, index) => {
        const error = validateFile(file);
        if (error) {
          errorDiv.textContent = error;
          errorDiv.style.display = 'block';
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          const container = document.createElement('div');
          container.className = 'preview-image-container';
          container.style.position = 'relative';
          container.style.display = 'inline-block';
          container.style.margin = '5px';

          const img = document.createElement('img');
          img.src = e.target.result;
          img.className = 'preview-image';
          img.alt = `照片 ${currentCount + index + 1}`;
          img.draggable = false;
          img.oncontextmenu = function() { return false; };

          const deleteBtn = document.createElement('button');
          deleteBtn.className = 'delete-btn';
          deleteBtn.innerHTML = '×';
          deleteBtn.style.position = 'absolute';
          deleteBtn.style.top = '-10px';
          deleteBtn.style.right = '-10px';
          deleteBtn.style.width = '20px';
          deleteBtn.style.height = '20px';
          deleteBtn.style.borderRadius = '50%';
          deleteBtn.style.background = '#ff4444';
          deleteBtn.style.color = 'white';
          deleteBtn.style.border = 'none';
          deleteBtn.style.cursor = 'pointer';
          deleteBtn.style.display = 'flex';
          deleteBtn.style.alignItems = 'center';
          deleteBtn.style.justifyContent = 'center';
          deleteBtn.style.fontSize = '16px';
          deleteBtn.style.padding = '0';
          deleteBtn.type = 'button';
          deleteBtn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            container.remove();
          };

          container.appendChild(img);
          container.appendChild(deleteBtn);
          preview.appendChild(container);
        }
        reader.readAsDataURL(file);
      });

      event.target.value = '';
    }

    // 打开购买模态框
    function openPurchaseModal(type = 'custom', panelNumber = null) {
      const modal = document.getElementById('purchaseModal');
      const modalContent = modal.querySelector('.modal-content');
      modal.style.display = 'block';
      modal.classList.add('show');
      modalContent.classList.add('centered');
      
      // 只在页面加载时生成订单ID，这里不再生成
      if (!document.getElementById('orderId').textContent) {
        document.getElementById('orderId').textContent = generateOrderId();
      }
      // 禁止主页面滚动
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
      // 设置分镜编号
      if (type === 'same' && panelNumber) {
        document.getElementById('productId').value = panelNumber;
        // 隐藏风格图和内容描述
        document.getElementById('styleUploadArea').style.display = 'none';
        document.getElementById('contentInputArea').style.display = 'none';
      } else {
        document.getElementById('productId').value = '';
        // 显示风格图和内容描述
        document.getElementById('styleUploadArea').style.display = '';
        document.getElementById('contentInputArea').style.display = '';
      }
    }

    // 关闭模态框
    function closeModal() {
      const modal = document.getElementById('purchaseModal');
      const modalContent = modal.querySelector('.modal-content');
      modal.style.display = 'none';
      modal.classList.remove('show');
      modalContent.classList.remove('centered');
      
      // 恢复主页面滚动
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    }

    // 提交前的数据收集
    async function goToPayment() {
      const submitBtn = document.getElementById('submitBtn');
      const spinner = submitBtn.querySelector('.spinner');
      
      try {
        // 显示加载状态
        submitBtn.disabled = true;
        spinner.style.display = 'inline-block';
        
        // 收集表单数据
        const formData = {
          orderId: document.getElementById('orderId').textContent,
          photos: Array.from(document.getElementById('photoPreview').children).map(img => img.src),
          styles: Array.from(document.getElementById('stylePreview').children).map(img => img.src),
          content: document.getElementById('contentInput').value,
          timestamp: new Date().toISOString()
        };
        
        // 校验
        if (formData.photos.length === 0) {
          showMessage('请至少上传一张照片！', 'error');
          resetSubmitButton();
          return;
        }
        // 只有定制时校验风格图和内容
        if (document.getElementById('styleUploadArea').style.display !== 'none') {
          if (formData.styles.length === 0) {
            showMessage('请至少上传一张风格图！', 'error');
            resetSubmitButton();
            return;
          }
          if (!formData.content.trim()) {
            showMessage('请填写内容描述！', 'error');
            resetSubmitButton();
            return;
          }
        }
        
        // 模拟提交延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 跳转到支付页面
        window.location.href = '支付链接';
        
      } catch (error) {
        showMessage('提交失败，请稍后重试', 'error');
        console.error('Form submission error:', error);
      } finally {
        resetSubmitButton();
      }
    }

    // 重置提交按钮状态
    function resetSubmitButton() {
      const submitBtn = document.getElementById('submitBtn');
      const spinner = submitBtn.querySelector('.spinner');
      submitBtn.disabled = false;
      spinner.style.display = 'none';
    }

    // 每个分镜独立对比组
    const compareData = {
      1: [
        { real: 'images2/d1f1z.jpg', anime: 'images2/d1f1d.jpg' },
        { real: 'images2/d1f1z2.jpg', anime: 'images2/d1f1d2.jpg' },
        // 可继续添加更多对比组
      ],
      2: [
        { real: 'images2/d1f2z.jpg', anime: 'images2/d1f2d.jpg' },
        { real: 'images2/d1f2z2.jpg', anime: 'images2/d1f2d2.jpg' },
      ],
      3: [
        { real: 'images2/d1f3z.jpg', anime: 'images2/d1f3d.jpg' },
        { real: 'images2/d1f3z2.jpg', anime: 'images2/d1f3d2.jpg' },
      ],
      4: [
        { real: 'images2/d1f4z.jpg', anime: 'images2/d1f4d.jpg' },
        { real: 'images2/d1f4z2.jpg', anime: 'images2/d1f4d2.jpg' },
      ],
      5: [
        { real: 'images2/d1f5z.jpg', anime: 'images2/d1f5d.jpg' },
        { real: 'images2/d1f5z2.jpg', anime: 'images2/d1f5d2.jpg' },
      ],
      6: [
        { real: 'images2/d1f6z.jpg', anime: 'images2/d1f6d.jpg' },
        { real: 'images2/d1f6z2.jpg', anime: 'images2/d1f6d2.jpg' },
        // 你可以后续添加分镜6的对比组
      ]
    };
    
    let currentList = [];
    let currentCompareIdx = 0;
    let currentPanelNumber = null;
    
    function openCompareModal(panelIdx) {
      currentPanelNumber = panelIdx;
      currentList = compareData[panelIdx] || [];
      currentCompareIdx = 0;
      showCompare();
      const modal = document.getElementById('compareModal');
      const modalContent = modal.querySelector('.modal-content');
      modal.style.display = 'block';
      modal.classList.add('show');
      modalContent.classList.add('centered');
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
    }
    
    function showCompare() {
      const waterfall = document.querySelector('.compare-waterfall');
      const noDataMessage = document.getElementById('noDataMessage');
      
      if(currentList.length === 0) {
        waterfall.innerHTML = '';
        noDataMessage.style.display = 'block';
        return;
      }
      
      noDataMessage.style.display = 'none';
      waterfall.innerHTML = '';
      
      // 渲染所有对比组
      currentList.forEach((item, index) => {
        const compareItem = document.createElement('div');
        compareItem.className = 'compare-item';
        
        // 预加载正面图片（动漫图）获取比例
        const animeImg = new Image();
        animeImg.onload = function() {
          const aspectRatio = animeImg.naturalWidth / animeImg.naturalHeight;
          
          // 翻转图标始终出现在第一张对比图上
          const flipIcon = index === 0 ? 'flip-icon' : '';
          
          compareItem.innerHTML = `
            <div class="flip-container ${flipIcon}" style="aspect-ratio: ${aspectRatio};">
              <div class="flip-front">
                <img src="${item.anime}" alt="动漫图" style="object-fit: contain; background: #f8f8f8;">
                <div class="label">动漫风格</div>
              </div>
              <div class="flip-back">
                <img src="${item.real}" alt="真人原图" style="object-fit: contain; background: #f8f8f8;">
                <div class="label">真人原图</div>
              </div>
            </div>
          `;
          
          // 添加点击翻转事件
          compareItem.addEventListener('click', function() {
            this.classList.toggle('flipped');
          });
          
          waterfall.appendChild(compareItem);
        };
        animeImg.src = item.anime;
      });
    }
    
    function updateNavButtons() {
      // 不再需要导航按钮
    }
    
    function prevCompare(e) {
      e.stopPropagation();
      if(currentList.length === 0) return;
      currentCompareIdx = (currentCompareIdx - 1 + currentList.length) % currentList.length;
      showCompare();
    }
    
    function nextCompare(e) {
      e.stopPropagation();
      if(currentList.length === 0) return;
      currentCompareIdx = (currentCompareIdx + 1) % currentList.length;
      showCompare();
    }
    
    function closeCompareModal(e) {
      if (e.target.id === 'compareModal' || e.target.classList.contains('close-btn')) {
        const modal = document.getElementById('compareModal');
        const modalContent = modal.querySelector('.modal-content');
        modal.style.display = 'none';
        modal.classList.remove('show');
        modalContent.classList.remove('centered');
        
        // 恢复主页面滚动
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        
        // 重置状态
        currentList = [];
        currentCompareIdx = 0;
      }
    }

    // 自动适应图片比例，动态设置分镜aspect-ratio
    document.querySelectorAll('.comic-container .panel img').forEach(img => {
      if (img.complete) {
        setPanelAspect(img);
      } else {
        img.onload = function() { setPanelAspect(img); };
      }
    });
    
    function setPanelAspect(img) {
      if (img.naturalWidth && img.naturalHeight) {
        const ratio = img.naturalWidth / img.naturalHeight;
        img.parentElement.style.aspectRatio = ratio;
      }
    }

    // 新增：性能监控
    function monitorPerformance() {
      if ('performance' in window) {
        window.addEventListener('load', function() {
          setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('页面加载时间:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            console.log('DOM内容加载时间:', perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart, 'ms');
          }, 0);
        });
      }
    }

    // 新增：错误处理
    window.addEventListener('error', function(e) {
      console.error('页面错误:', e.error);
      // 可以在这里添加错误上报逻辑
    });

    // 新增：离线检测
    window.addEventListener('online', function() {
      showMessage('网络连接已恢复', 'success');
    });

    window.addEventListener('offline', function() {
      showMessage('网络连接已断开', 'error');
    });

    // 新增：图片懒加载增强
    function initLazyLoading() {
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              imageObserver.unobserve(img);
            }
          });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
          imageObserver.observe(img);
        });
      }
    }

    // 新增：分享功能
    function sharePage() {
      if (navigator.share) {
        navigator.share({
          title: '漫画风格艺术展示',
          text: '专业真人转动漫风格定制服务',
          url: window.location.href
        });
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
          showMessage('链接已复制到剪贴板', 'success');
        });
      }
    }

    // 图片和对比数据
    let autoImages = [];

    // 从JSON文件加载图片列表
    async function loadCoverImages() {
      try {
        const response = await fetch('images1/coverImages.json');
        autoImages = await response.json();
        renderAutoCompareImages();
      } catch (error) {
        console.error('加载封面图片列表失败:', error);
      }
    }

    // 从JSON文件加载对比数据
    async function loadCompareData() {
      try {
        const response = await fetch('images2/compareImages.json');
        const compareImages = await response.json();
        
        // 将图片数组转换为对比对象
        const compareData = {};
        for (let i = 0; i < compareImages.length; i += 2) {
          const animeImage = compareImages[i];
          const realImage = compareImages[i + 1];
          const panelNumber = animeImage.match(/d7f(\d+)/)[1];
          compareData[panelNumber] = [{
            real: `images2/${realImage}`,
            anime: `images2/${animeImage}`
          }];
        }
        return compareData;
      } catch (error) {
        console.error('加载对比数据失败:', error);
        // 添加测试数据
        return {
          '1': [
            { real: 'images1/d7f1.webp', anime: 'images1/d7f2.png' },
            { real: 'images1/d7f3.png', anime: 'images1/d7f4.webp' }
          ],
          '2': [
            { real: 'images1/d7f5.jpg', anime: 'images1/d7f8.png' },
            { real: 'images1/d7f10.png', anime: 'images1/d7f11.png' }
          ],
          '3': [
            { real: 'images1/d7f12.png', anime: 'images1/d7f13.png' },
            { real: 'images1/d7f14.png', anime: 'images1/d7f18.png' }
          ]
        };
      }
    }

    let autoCompareData = {};
    let autoCurrentList = [];
    let autoCurrentIdx = 0;

    // 初始化
    async function initializeCompare() {
      autoCompareData = await loadCompareData();
      await loadCoverImages();  // 加载封面图片列表
    }

    function openAutoCompareModalDirect(panelNumber) {
      const compareData = autoCompareData[panelNumber] || [];
      currentList = compareData;  // 使用currentList而不是autoCurrentList
      currentCompareIdx = 0;      // 使用currentCompareIdx而不是autoCurrentIdx
      currentPanelNumber = panelNumber;  // 设置当前分镜编号
      showCompare();  // 使用新的showCompare函数
      const modal = document.getElementById('compareModal');
      const modalContent = modal.querySelector('.modal-content');
      modal.style.display = 'block';
      modal.classList.add('show');
      modalContent.classList.add('centered');
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
    }

    // 随机打乱数组
    function shuffleArray(arr) {
      for (let i = arr.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [arr[i], arr[j]] = [arr[j], arr[i]];
      }
    }

    // 动态渲染图片
    function renderAutoCompareImages() {
      const container = document.getElementById('autoCompareContainer');
      if (!container) {
        console.error('找不到autoCompareContainer元素');
        return;
      }
      container.innerHTML = '';
      
      // 随机打乱图片顺序
      shuffleArray(autoImages);
      
      // 绑定图片和对比数据
      const paired = autoImages.map((img, idx) => {
        const match = img.match(/d7f(\d+)/);
        if (!match) {
          console.error('图片名称格式错误:', img);
          return null;
        }
        const panelNumber = match[1];
        return {
          img: img,
          panelNumber,
          compare: autoCompareData[panelNumber] || []
        };
      }).filter(item => item !== null);
      
      paired.forEach((item) => {
        const src = `images1/${item.img}`;
        const panelNumber = item.panelNumber;
        const itemDiv = document.createElement('div');
        itemDiv.className = 'auto-img-item';
        itemDiv.onclick = () => openAutoCompareModalDirect(panelNumber);
        
        // 图片
        const imgEl = document.createElement('img');
        imgEl.src = src;
        imgEl.alt = '';
        imgEl.loading = 'lazy';
        imgEl.draggable = false;
        imgEl.oncontextmenu = () => false;
        imgEl.onerror = function() {
          console.error('图片加载失败:', src);
          this.src = '';
        };
        imgEl.onload = function() {
          console.log('图片加载成功:', src);
        };
        
        // 组装（不再添加编号）
        itemDiv.appendChild(imgEl);
        container.appendChild(itemDiv);
      });
      
      console.log('渲染完成，容器内容:', container.innerHTML);
    }

    // 初始化
    initializeCompare();

    function showAutoCompare() {
      const realImg = document.getElementById('realImg');
      const animeImg = document.getElementById('animeImg');
      const noDataMessage = document.getElementById('noDataMessage');
      const compareFlex = document.querySelector('.compare-flex');
      const prevBtn = document.getElementById('prevBtn');
      const nextBtn = document.getElementById('nextBtn');
      
      if(autoCurrentList.length === 0) {
        realImg.style.display = 'none';
        animeImg.style.display = 'none';
        compareFlex.style.display = 'none';
        noDataMessage.style.display = 'block';
        return;
      }
      
      realImg.style.display = 'block';
      animeImg.style.display = 'block';
      compareFlex.style.display = 'flex';
      noDataMessage.style.display = 'none';
      
      realImg.src = autoCurrentList[autoCurrentIdx].real;
      animeImg.src = autoCurrentList[autoCurrentIdx].anime;
      
      realImg.onerror = function() {
        this.src = '';
      };
      animeImg.onerror = function() {
        this.src = '';
      };
    }

    function prevAutoCompare(e) {
      e.stopPropagation();
      if(autoCurrentList.length <= 1) {
        showNoMoreMessage();
        return;
      }
      autoCurrentIdx = (autoCurrentIdx - 1 + autoCurrentList.length) % autoCurrentList.length;
      showAutoCompare();
    }

    function nextAutoCompare(e) {
      e.stopPropagation();
      if(autoCurrentList.length <= 1) {
        showNoMoreMessage();
        return;
      }
      autoCurrentIdx = (autoCurrentIdx + 1) % autoCurrentList.length;
      showAutoCompare();
    }

    function showNoMoreMessage() {
      let msg = document.getElementById('noMoreMsg');
      if (!msg) {
        msg = document.createElement('div');
        msg.id = 'noMoreMsg';
        msg.style.position = 'absolute';
        msg.style.top = '24px';
        msg.style.left = '50%';
        msg.style.transform = 'translateX(-50%)';
        msg.style.background = 'rgba(0,0,0,0.8)';
        msg.style.color = '#fff';
        msg.style.padding = '8px 20px';
        msg.style.borderRadius = '8px';
        msg.style.zIndex = '2000';
        msg.style.fontSize = '1.1em';
        msg.textContent = '暂无更多图片';
        document.querySelector('.compare-modal-content').appendChild(msg);
      }
      msg.style.display = 'block';
      setTimeout(() => { msg.style.display = 'none'; }, 2000);
    }

    // 打开制作同款模态框
    function openMakeSameModal(panelNumber = null) {
      const modal = document.getElementById('makeSameModal');
      const modalContent = modal.querySelector('.modal-content');
      modal.style.display = 'block';
      modal.classList.add('show');
      modalContent.classList.add('centered');
      // 生成订单ID
      document.getElementById('makeSameOrderId').textContent = generateOrderId();
      // 设置分镜编号
      document.getElementById('makeSameProductId').value = panelNumber || '';
      // 禁止主页面滚动
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
    }

    // 关闭制作同款模态框
    function closeMakeSameModal() {
      const modal = document.getElementById('makeSameModal');
      const modalContent = modal.querySelector('.modal-content');
      modal.style.display = 'none';
      modal.classList.remove('show');
      modalContent.classList.remove('centered');
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
      // 清除分镜编号
      document.getElementById('makeSameProductId').value = '';
    }

    // 复制ID（制作同款）
    async function copyMakeSameOrderId() {
      const id = document.getElementById('makeSameOrderId').textContent;
      try {
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(id);
          showMessage('ID已复制到剪贴板！', 'success');
        } else {
          const textArea = document.createElement('textarea');
          textArea.value = id;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          try {
            document.execCommand('copy');
            showMessage('ID已复制到剪贴板！', 'success');
          } catch (err) {
            showMessage('复制失败，请手动复制', 'error');
          }
          textArea.remove();
        }
      } catch (err) {
        showMessage('复制失败，请手动复制', 'error');
      }
    }

    // 处理照片上传（制作同款）
    function handleMakeSamePhotoUpload(event) {
      event.preventDefault();
      const files = Array.from(event.target.files);
      const preview = document.getElementById('makeSamePhotoPreview');
      const errorDiv = document.getElementById('makeSamePhotoError');
      errorDiv.style.display = 'none';
      errorDiv.textContent = '';
      const currentCount = preview.children.length;
      if (currentCount + files.length > CONFIG.MAX_PHOTOS) {
        errorDiv.textContent = `最多只能上传 ${CONFIG.MAX_PHOTOS} 张照片，当前已有 ${currentCount} 张`;
        errorDiv.style.display = 'block';
        return;
      }
      files.forEach((file, index) => {
        const error = validateFile(file);
        if (error) {
          errorDiv.textContent = error;
          errorDiv.style.display = 'block';
          return;
        }
        const reader = new FileReader();
        reader.onload = function(e) {
          const container = document.createElement('div');
          container.className = 'preview-image-container';
          container.style.position = 'relative';
          container.style.display = 'inline-block';
          container.style.margin = '5px';
          const img = document.createElement('img');
          img.src = e.target.result;
          img.className = 'preview-image';
          img.alt = `照片 ${currentCount + index + 1}`;
          img.draggable = false;
          img.oncontextmenu = function() { return false; };
          const deleteBtn = document.createElement('button');
          deleteBtn.className = 'delete-btn';
          deleteBtn.innerHTML = '×';
          deleteBtn.style.position = 'absolute';
          deleteBtn.style.top = '-10px';
          deleteBtn.style.right = '-10px';
          deleteBtn.style.width = '20px';
          deleteBtn.style.height = '20px';
          deleteBtn.style.borderRadius = '50%';
          deleteBtn.style.background = '#ff4444';
          deleteBtn.style.color = 'white';
          deleteBtn.style.border = 'none';
          deleteBtn.style.cursor = 'pointer';
          deleteBtn.style.display = 'flex';
          deleteBtn.style.alignItems = 'center';
          deleteBtn.style.justifyContent = 'center';
          deleteBtn.style.fontSize = '16px';
          deleteBtn.style.padding = '0';
          deleteBtn.type = 'button';
          deleteBtn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            container.remove();
          };
          container.appendChild(img);
          container.appendChild(deleteBtn);
          preview.appendChild(container);
        }
        reader.readAsDataURL(file);
      });
      event.target.value = '';
    }

    // 提交前的数据收集（制作同款）
    async function goToMakeSamePayment() {
      const submitBtn = document.getElementById('makeSameSubmitBtn');
      const spinner = submitBtn.querySelector('.spinner');
      try {
        submitBtn.disabled = true;
        spinner.style.display = 'inline-block';
        const formData = {
          orderId: document.getElementById('makeSameOrderId').textContent,
          photos: Array.from(document.getElementById('makeSamePhotoPreview').children).map(img => img.src),
          productId: document.getElementById('makeSameProductId').value,
          timestamp: new Date().toISOString()
        };
        if (formData.photos.length === 0) {
          showMessage('请至少上传一张照片！', 'error');
          submitBtn.disabled = false;
          spinner.style.display = 'none';
          return;
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
        window.location.href = '支付链接';
      } catch (error) {
        showMessage('提交失败，请稍后重试', 'error');
        console.error('Form submission error:', error);
      } finally {
        submitBtn.disabled = false;
        spinner.style.display = 'none';
      }
    }

    // 修改"制作同款"按钮点击事件
    document.getElementById('makeSameBtn').onclick = function() {
      // 获取当前分镜编号
      let panelNumber = currentPanelNumber;
      // 先关闭对比弹窗
      const compareModal = document.getElementById('compareModal');
      compareModal.style.display = 'none';
      compareModal.classList.remove('show');
      compareModal.querySelector('.modal-content').classList.remove('centered');
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
      // 打开制作同款弹窗
      openMakeSameModal(panelNumber);
    };

    function handleStyleUpload(event) {
      event.preventDefault();
      const files = Array.from(event.target.files);
      const preview = document.getElementById('stylePreview');
      const errorDiv = document.getElementById('styleError');
      // 调试输出
      console.log('风格图上传', preview, files);
      // 清空错误提示
      errorDiv.style.display = 'none';
      errorDiv.textContent = '';
      // 检查总数是否超过限制
      const currentCount = preview ? preview.children.length : 0;
      if (currentCount + files.length > CONFIG.MAX_STYLES) {
        errorDiv.textContent = `最多只能上传 ${CONFIG.MAX_STYLES} 张风格图，当前已有 ${currentCount} 张`;
        errorDiv.style.display = 'block';
        return;
      }
      files.forEach((file, index) => {
        const error = validateFile(file);
        if (error) {
          errorDiv.textContent = error;
          errorDiv.style.display = 'block';
          return;
        }
        const reader = new FileReader();
        reader.onload = function(e) {
          const container = document.createElement('div');
          container.className = 'preview-image-container';
          container.style.position = 'relative';
          container.style.display = 'inline-block';
          container.style.margin = '5px';
          const img = document.createElement('img');
          img.src = e.target.result;
          img.className = 'preview-image';
          img.alt = `风格图 ${currentCount + index + 1}`;
          img.draggable = false;
          img.oncontextmenu = function() { return false; };
          const deleteBtn = document.createElement('button');
          deleteBtn.className = 'delete-btn';
          deleteBtn.innerHTML = '×';
          deleteBtn.style.position = 'absolute';
          deleteBtn.style.top = '-10px';
          deleteBtn.style.right = '-10px';
          deleteBtn.style.width = '20px';
          deleteBtn.style.height = '20px';
          deleteBtn.style.borderRadius = '50%';
          deleteBtn.style.background = '#ff4444';
          deleteBtn.style.color = 'white';
          deleteBtn.style.border = 'none';
          deleteBtn.style.cursor = 'pointer';
          deleteBtn.style.display = 'flex';
          deleteBtn.style.alignItems = 'center';
          deleteBtn.style.justifyContent = 'center';
          deleteBtn.style.fontSize = '16px';
          deleteBtn.style.padding = '0';
          deleteBtn.type = 'button';
          deleteBtn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            container.remove();
          };
          container.appendChild(img);
          container.appendChild(deleteBtn);
          if (preview) preview.appendChild(container);
        }
        reader.readAsDataURL(file);
      });
      event.target.value = '';
    }

    function openFAQModal() {
      const modal = document.getElementById('faqModal');
      modal.style.display = 'block';
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    }
    function closeFAQModal() {
      const modal = document.getElementById('faqModal');
      modal.style.display = 'none';
      modal.classList.remove('show');
      document.body.style.overflow = '';
    }
    document.getElementById('faqModal').onclick = function(e) {
      if (e.target.id === 'faqModal') closeFAQModal();
    };

    // FAQ折叠功能
    function toggleFAQ(num) {
      const answer = document.getElementById('answer' + num);
      const icon = document.getElementById('icon' + num);
      
      if (answer.style.display === 'none' || answer.style.display === '') {
        answer.style.display = 'block';
        icon.textContent = '−';
      } else {
        answer.style.display = 'none';
        icon.textContent = '+';
      }
    }
  </script>
</body>
</html> 